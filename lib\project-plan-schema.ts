import { z } from 'zod'

/**
 * AG3NT Project Plan Schema
 * 
 * Defines the structure for project plans that can be uploaded
 * to skip the planning phase and go directly to coding
 */

// Tech Stack Schema
export const TechStackSchema = z.object({
  Frontend: z.string().optional(),
  Backend: z.string().optional(),
  Database: z.string().optional(),
  Hosting: z.string().optional(),
  Authentication: z.string().optional(),
  Mobile: z.string().optional(),
  DevOps: z.string().optional(),
  Testing: z.string().optional(),
})

export type TechStack = z.infer<typeof TechStackSchema>

// Architecture Schema
export const ArchitectureSchema = z.object({
  type: z.string().optional(),
  pattern: z.string().optional(),
  components: z.array(z.string()).optional(),
  dataFlow: z.string().optional(),
  scalability: z.string().optional(),
})

export type Architecture = z.infer<typeof ArchitectureSchema>

// Feature Schema
export const FeatureSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  priority: z.enum(['high', 'medium', 'low']),
  complexity: z.enum(['simple', 'moderate', 'complex']),
  estimatedTime: z.string().optional(),
  dependencies: z.array(z.string()).optional(),
})

export type Feature = z.infer<typeof FeatureSchema>

// Wireframe Schema
export const WireframeSchema = z.object({
  pages: z.array(z.object({
    name: z.string(),
    path: z.string(),
    description: z.string(),
    wireframe: z.string(), // ASCII wireframe
    components: z.array(z.string()).optional(),
  })).optional(),
  components: z.array(z.object({
    name: z.string(),
    type: z.string(),
    description: z.string(),
    props: z.array(z.string()).optional(),
  })).optional(),
})

export type Wireframes = z.infer<typeof WireframeSchema>

// Database Schema
export const DatabaseSchemaType = z.object({
  tables: z.array(z.object({
    name: z.string(),
    description: z.string(),
    fields: z.array(z.object({
      name: z.string(),
      type: z.string(),
      required: z.boolean().optional(),
      unique: z.boolean().optional(),
      description: z.string().optional(),
    })),
    relationships: z.array(z.object({
      type: z.string(),
      table: z.string(),
      field: z.string(),
    })).optional(),
  })).optional(),
  relationships: z.array(z.object({
    from: z.string(),
    to: z.string(),
    type: z.string(),
    description: z.string().optional(),
  })).optional(),
})

export type DatabaseSchema = z.infer<typeof DatabaseSchemaType>

// File System Schema
export const FileSystemSchema = z.object({
  structure: z.record(z.any()).optional(),
  directories: z.array(z.string()).optional(),
  files: z.array(z.object({
    path: z.string(),
    type: z.string(),
    description: z.string().optional(),
  })).optional(),
})

export type FileSystem = z.infer<typeof FileSystemSchema>

// Task Schema
export const TaskSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  type: z.enum(['frontend', 'backend', 'database', 'testing', 'deployment', 'integration']),
  priority: z.enum(['high', 'medium', 'low']),
  estimatedTime: z.string().optional(),
  dependencies: z.array(z.string()).optional(),
  assignedTo: z.string().optional(),
})

export type Task = z.infer<typeof TaskSchema>

// Main Project Plan Schema
export const ProjectPlanSchema = z.object({
  // Basic Information
  projectName: z.string(),
  projectDescription: z.string(),
  version: z.string().default('1.0.0'),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
  
  // Planning Results
  analysis: z.object({
    projectType: z.string().optional(),
    complexity: z.string().optional(),
    targetAudience: z.string().optional(),
    keyFeatures: z.array(z.string()).optional(),
    technicalRequirements: z.array(z.string()).optional(),
  }).optional(),
  
  techStack: TechStackSchema,
  architecture: ArchitectureSchema.optional(),
  features: z.array(FeatureSchema).optional(),
  wireframes: WireframeSchema.optional(),
  database: DatabaseSchemaType.optional(),
  filesystem: FileSystemSchema.optional(),
  tasks: z.array(TaskSchema).optional(),
  
  // Design Information
  design: z.object({
    theme: z.string().optional(),
    colorScheme: z.string().optional(),
    typography: z.string().optional(),
    layout: z.string().optional(),
    responsive: z.boolean().optional(),
  }).optional(),
  
  // Workflow Information
  workflow: z.object({
    phases: z.array(z.string()).optional(),
    milestones: z.array(z.object({
      name: z.string(),
      description: z.string(),
      deadline: z.string().optional(),
    })).optional(),
    estimatedDuration: z.string().optional(),
  }).optional(),
  
  // Metadata
  metadata: z.object({
    source: z.string().optional(), // 'upload', 'planning-agent', 'manual'
    planningMethod: z.string().optional(),
    confidence: z.number().optional(),
    tags: z.array(z.string()).optional(),
  }).optional(),
})

export type ProjectPlan = z.infer<typeof ProjectPlanSchema>

// Validation function
export function validateProjectPlan(data: unknown): { success: boolean; data?: ProjectPlan; errors?: string[] } {
  try {
    const result = ProjectPlanSchema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      return { success: false, errors }
    }
    return { success: false, errors: ['Invalid project plan format'] }
  }
}

// Sample project plan for reference
export const sampleProjectPlan: ProjectPlan = {
  projectName: "Task Management App",
  projectDescription: "A modern task management application with real-time collaboration",
  version: "1.0.0",
  
  analysis: {
    projectType: "Web Application",
    complexity: "Moderate",
    targetAudience: "Teams and individuals",
    keyFeatures: ["Task creation", "Real-time updates", "Team collaboration", "Progress tracking"],
    technicalRequirements: ["Responsive design", "Real-time sync", "User authentication"]
  },
  
  techStack: {
    Frontend: "Next.js with TypeScript",
    Backend: "Node.js with Express",
    Database: "PostgreSQL with Prisma",
    Hosting: "Vercel",
    Authentication: "NextAuth.js"
  },
  
  architecture: {
    type: "Full-stack web application",
    pattern: "MVC with API routes",
    components: ["Frontend SPA", "REST API", "Database", "Authentication"],
    dataFlow: "Client -> API -> Database",
    scalability: "Horizontal scaling with load balancer"
  },
  
  features: [
    {
      id: "task-crud",
      name: "Task CRUD Operations",
      description: "Create, read, update, and delete tasks",
      priority: "high",
      complexity: "simple",
      estimatedTime: "2-3 hours"
    },
    {
      id: "real-time-sync",
      name: "Real-time Synchronization",
      description: "Live updates across all connected clients",
      priority: "high",
      complexity: "moderate",
      estimatedTime: "4-6 hours"
    }
  ],
  
  tasks: [
    {
      id: "setup-database",
      title: "Setup Database Schema",
      description: "Create database tables and relationships",
      type: "database",
      priority: "high",
      estimatedTime: "1-2 hours"
    },
    {
      id: "create-api",
      title: "Create API Endpoints",
      description: "Build REST API for task operations",
      type: "backend",
      priority: "high",
      estimatedTime: "3-4 hours",
      dependencies: ["setup-database"]
    },
    {
      id: "build-ui",
      title: "Build User Interface",
      description: "Create React components and pages",
      type: "frontend",
      priority: "high",
      estimatedTime: "4-6 hours",
      dependencies: ["create-api"]
    }
  ],
  
  metadata: {
    source: "upload",
    planningMethod: "manual",
    confidence: 0.9,
    tags: ["task-management", "collaboration", "productivity"]
  }
}

// Export utility functions
export { ProjectPlanSchema as default }
