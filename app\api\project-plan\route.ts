import { NextRequest, NextResponse } from 'next/server'
import {
  validateProjectPlan,
  ProjectPlan,
  isAG3NTPlanningResults,
  extractProjectInfoFromAG3NT,
  AG3NTPlanningResults
} from '@/lib/project-plan-schema'
import { codingOrchestrator } from '@/lib/coding-workflow-orchestrator'

export const maxDuration = 60

/**
 * POST /api/project-plan
 * Upload a project plan JSON and start coding workflow
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    
    // Validate the uploaded project plan
    const validation = validateProjectPlan(body)
    
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid project plan format',
        details: validation.errors
      }, { status: 400 })
    }

    const projectPlan = validation.data!
    const planType = validation.type!

    // Extract project info based on plan type
    let projectInfo: {
      projectName: string
      projectDescription: string
      techStack: any
      tasksCount: number
    }

    if (planType === 'ag3nt' && isAG3NTPlanningResults(projectPlan)) {
      const extracted = extractProjectInfoFromAG3NT(projectPlan)
      projectInfo = {
        ...extracted,
        tasksCount: projectPlan.results.tasks?.breakdown?.length ||
                   (Array.isArray(projectPlan.results.tasks) ? projectPlan.results.tasks.length : 0)
      }
      console.log(`📋 AG3NT planning results uploaded: ${projectInfo.projectName}`)
    } else {
      // Legacy format
      const legacy = projectPlan as any
      projectInfo = {
        projectName: legacy.projectName,
        projectDescription: legacy.projectDescription,
        techStack: legacy.techStack,
        tasksCount: legacy.tasks?.length || 0
      }
      console.log(`📋 Legacy project plan uploaded: ${projectInfo.projectName}`)
    }

    // Store the project plan (you might want to save to database here)
    // For now, we'll just pass it to the coding orchestrator

    // Start coding workflow with the uploaded plan
    if (codingOrchestrator) {
      try {
        // Set the project plan in the orchestrator
        await codingOrchestrator.startWorkflow(projectPlan)

        console.log('🚀 Coding workflow started with uploaded project plan')

        return NextResponse.json({
          success: true,
          message: `${planType === 'ag3nt' ? 'AG3NT planning results' : 'Project plan'} uploaded and coding workflow started`,
          data: {
            ...projectInfo,
            planType,
            workflowStarted: true
          }
        })
        
      } catch (error) {
        console.error('Failed to start coding workflow:', error)
        return NextResponse.json({
          success: false,
          error: 'Failed to start coding workflow',
          details: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 })
      }
    } else {
      return NextResponse.json({
        success: false,
        error: 'Coding orchestrator not available'
      }, { status: 503 })
    }
    
  } catch (error) {
    console.error('Error processing project plan upload:', error)
    
    if (error instanceof SyntaxError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid JSON format',
        details: 'Please ensure your project plan is valid JSON'
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

/**
 * GET /api/project-plan
 * Get sample project plan or current project plan
 */
export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url)
  const action = searchParams.get('action')
  
  try {
    switch (action) {
      case 'sample':
        // Return a sample project plan
        const { sampleProjectPlan } = await import('@/lib/project-plan-schema')
        return NextResponse.json({
          success: true,
          data: sampleProjectPlan,
          message: 'Sample project plan'
        })
        
      case 'current':
        // Return current project plan if available
        if (codingOrchestrator) {
          const currentPlan = codingOrchestrator.getProjectPlan()
          if (currentPlan) {
            return NextResponse.json({
              success: true,
              data: currentPlan,
              message: 'Current project plan'
            })
          }
        }
        
        return NextResponse.json({
          success: false,
          error: 'No current project plan available'
        }, { status: 404 })
        
      case 'schema':
        // Return the project plan schema for reference
        return NextResponse.json({
          success: true,
          data: {
            description: 'AG3NT Project Plan Schema',
            requiredFields: [
              'projectName',
              'projectDescription', 
              'techStack'
            ],
            optionalFields: [
              'analysis',
              'architecture',
              'features',
              'wireframes',
              'database',
              'filesystem',
              'tasks',
              'design',
              'workflow',
              'metadata'
            ],
            example: '/api/project-plan?action=sample'
          },
          message: 'Project plan schema information'
        })
        
      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action',
          availableActions: ['sample', 'current', 'schema']
        }, { status: 400 })
    }
    
  } catch (error) {
    console.error('Error handling project plan GET request:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

/**
 * DELETE /api/project-plan
 * Clear current project plan
 */
export async function DELETE(req: NextRequest) {
  try {
    if (codingOrchestrator) {
      // Stop current workflow if running
      if (codingOrchestrator.isWorkflowRunning()) {
        await codingOrchestrator.stopWorkflow()
      }
      
      // Clear project plan
      codingOrchestrator.clearProjectPlan()
      
      return NextResponse.json({
        success: true,
        message: 'Project plan cleared and workflow stopped'
      })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Coding orchestrator not available'
    }, { status: 503 })
    
  } catch (error) {
    console.error('Error clearing project plan:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to clear project plan',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
