/**
 * AG3NT Platform - E2B Sandbox Preview Component
 * 
 * Displays live E2B sandbox applications with controls and status
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Globe, 
  ExternalLink, 
  RefreshCw, 
  Play, 
  Square, 
  Monitor,
  Smartphone,
  Tablet,
  Code,
  Terminal,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'
import { StreamingUpdate } from '@/lib/e2b-schema'

interface E2BSandbox {
  id: string
  url: string
  status: 'creating' | 'ready' | 'error'
  template: string
  taskId?: string
  title?: string
  description?: string
  framework?: string
  language?: string
  port?: number
  createdAt?: number
}

interface E2BPreviewProps {
  sandboxes: E2BSandbox[]
  activeSandbox?: E2BSandbox | null
  onSandboxSelect?: (sandbox: E2BSandbox) => void
  onRefresh?: (sandboxId: string) => void
  streamingUpdates?: StreamingUpdate[]
  className?: string
}

export default function E2BPreview({ 
  sandboxes, 
  activeSandbox, 
  onSandboxSelect, 
  onRefresh,
  streamingUpdates = [],
  className = '' 
}: E2BPreviewProps) {
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [previewError, setPreviewError] = useState<string | null>(null)

  const handleRefresh = async (sandboxId: string) => {
    setIsRefreshing(true)
    setPreviewError(null)
    
    try {
      if (onRefresh) {
        await onRefresh(sandboxId)
      }
    } catch (error) {
      setPreviewError(error instanceof Error ? error.message : 'Failed to refresh sandbox')
    } finally {
      setIsRefreshing(false)
    }
  }

  const openInNewTab = (sandbox: E2BSandbox) => {
    if (sandbox.url) {
      window.open(sandbox.url, '_blank')
    }
  }

  const getStatusIcon = (status: E2BSandbox['status']) => {
    switch (status) {
      case 'creating':
        return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />
      case 'ready':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <Globe className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusText = (status: E2BSandbox['status']) => {
    switch (status) {
      case 'creating':
        return 'Creating...'
      case 'ready':
        return 'Ready'
      case 'error':
        return 'Error'
      default:
        return 'Unknown'
    }
  }

  const getViewportDimensions = () => {
    switch (viewMode) {
      case 'mobile':
        return { width: '375px', height: '667px' }
      case 'tablet':
        return { width: '768px', height: '1024px' }
      case 'desktop':
      default:
        return { width: '100%', height: '600px' }
    }
  }

  const recentUpdates = streamingUpdates
    .filter(update => activeSandbox && update.taskId === activeSandbox.taskId)
    .slice(-3)

  if (sandboxes.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Globe className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Live Previews Available</h3>
          <p className="text-muted-foreground text-center max-w-md">
            E2B sandbox previews will appear here when frontend tasks are completed. 
            Start the autonomous coding workflow to see live applications.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Globe className="h-5 w-5 text-blue-500" />
            <CardTitle className="text-lg">Live E2B Preview</CardTitle>
          </div>
          <Badge variant="outline">
            {sandboxes.length} sandbox{sandboxes.length !== 1 ? 'es' : ''}
          </Badge>
        </div>
        <CardDescription>
          Real-time preview of generated applications running in E2B sandboxes
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="preview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="sandboxes">Sandboxes</TabsTrigger>
            <TabsTrigger value="console">Console</TabsTrigger>
          </TabsList>

          <TabsContent value="preview" className="space-y-4">
            {activeSandbox ? (
              <div className="space-y-4">
                {/* Sandbox Info */}
                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(activeSandbox.status)}
                    <div>
                      <div className="font-medium">
                        {activeSandbox.title || `${activeSandbox.template} - ${activeSandbox.id.slice(0, 8)}`}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {activeSandbox.framework} • Port {activeSandbox.port || 3000}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{getStatusText(activeSandbox.status)}</Badge>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleRefresh(activeSandbox.id)}
                      disabled={isRefreshing}
                    >
                      <RefreshCw className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => openInNewTab(activeSandbox)}
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                {/* Viewport Controls */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">Viewport:</span>
                    <div className="flex space-x-1">
                      <Button
                        size="sm"
                        variant={viewMode === 'desktop' ? 'default' : 'outline'}
                        onClick={() => setViewMode('desktop')}
                      >
                        <Monitor className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant={viewMode === 'tablet' ? 'default' : 'outline'}
                        onClick={() => setViewMode('tablet')}
                      >
                        <Tablet className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant={viewMode === 'mobile' ? 'default' : 'outline'}
                        onClick={() => setViewMode('mobile')}
                      >
                        <Smartphone className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {activeSandbox.url}
                  </div>
                </div>

                {/* Preview Frame */}
                {activeSandbox.status === 'ready' ? (
                  <div className="border rounded-lg overflow-hidden bg-white">
                    <div 
                      className="mx-auto transition-all duration-300"
                      style={getViewportDimensions()}
                    >
                      <iframe
                        src={activeSandbox.url}
                        className="w-full h-full border-0"
                        title={`Preview: ${activeSandbox.title || activeSandbox.id}`}
                        sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                        onError={() => setPreviewError('Failed to load preview')}
                      />
                    </div>
                  </div>
                ) : activeSandbox.status === 'creating' ? (
                  <div className="flex flex-col items-center justify-center py-12 border rounded-lg">
                    <Clock className="h-8 w-8 text-yellow-500 animate-spin mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Creating Sandbox...</h3>
                    <p className="text-muted-foreground">Setting up your application environment</p>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12 border rounded-lg">
                    <AlertCircle className="h-8 w-8 text-red-500 mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Preview Unavailable</h3>
                    <p className="text-muted-foreground">
                      {previewError || 'There was an error loading the preview'}
                    </p>
                    <Button
                      className="mt-4"
                      onClick={() => handleRefresh(activeSandbox.id)}
                      disabled={isRefreshing}
                    >
                      <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                      Retry
                    </Button>
                  </div>
                )}

                {/* Recent Updates */}
                {recentUpdates.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Recent Updates</h4>
                    <div className="space-y-1">
                      {recentUpdates.map((update, index) => (
                        <div key={index} className="text-xs p-2 bg-muted rounded flex items-center space-x-2">
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                          <span>{update.message}</span>
                          <span className="text-muted-foreground ml-auto">
                            {new Date(update.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center text-muted-foreground py-8">
                <Monitor className="h-8 w-8 mx-auto mb-2" />
                <p>Select a sandbox to preview</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="sandboxes" className="space-y-4">
            <div className="space-y-2">
              {sandboxes.map((sandbox) => (
                <div 
                  key={sandbox.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    activeSandbox?.id === sandbox.id 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-950' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => onSandboxSelect?.(sandbox)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(sandbox.status)}
                      <div>
                        <div className="font-medium">
                          {sandbox.title || `${sandbox.template} - ${sandbox.id.slice(0, 8)}`}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {sandbox.framework} • {sandbox.language}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{getStatusText(sandbox.status)}</Badge>
                      {sandbox.status === 'ready' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation()
                            openInNewTab(sandbox)
                          }}
                        >
                          <ExternalLink className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="console" className="space-y-4">
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm h-64 overflow-y-auto">
              <div className="space-y-1">
                {streamingUpdates.slice(-10).map((update, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <span className="text-gray-500 text-xs">
                      {new Date(update.timestamp).toLocaleTimeString()}
                    </span>
                    <span className={`text-xs ${
                      update.type === 'error' ? 'text-red-400' :
                      update.type === 'completion' ? 'text-green-400' :
                      update.type === 'generation' ? 'text-yellow-400' :
                      'text-blue-400'
                    }`}>
                      [{update.type.toUpperCase()}]
                    </span>
                    <span className="text-xs">{update.message}</span>
                  </div>
                ))}
                {streamingUpdates.length === 0 && (
                  <div className="text-gray-500 text-xs">No console output yet...</div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
