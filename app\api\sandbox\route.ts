import { NextRequest, NextResponse } from 'next/server'
import { Sandbox } from '@e2b/code-interpreter'

const sandboxTimeout = 10 * 60 * 1000 // 10 minutes in ms

export const maxDuration = 60

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
}

// Retry utility function
async function withRetry<T>(
  operation: () => Promise<T>,
  retries: number = RETRY_CONFIG.maxRetries
): Promise<T> {
  try {
    return await operation()
  } catch (error) {
    if (retries > 0) {
      const delay = Math.min(
        RETRY_CONFIG.baseDelay * Math.pow(2, RETRY_CONFIG.maxRetries - retries),
        RETRY_CONFIG.maxDelay
      )
      console.log(`Retrying operation in ${delay}ms... (${retries} retries left)`)
      await new Promise(resolve => setTimeout(resolve, delay))
      return withRetry(operation, retries - 1)
    }
    throw error
  }
}

interface SandboxFile {
  file_path: string
  file_name?: string
  file_content: string
  file_type?: string
}

interface SandboxRequest {
  template: string
  files?: SandboxFile[]
  code?: string
  file_path?: string
  port?: number
  is_multi_file?: boolean
  has_additional_dependencies?: boolean
  install_dependencies_command?: string
  additional_dependencies?: string[]
  userID?: string
  teamID?: string
  accessToken?: string
}

interface ExecutionResultInterpreter {
  sbxId: string
  template: string
  stdout: string
  stderr: string
  runtimeError?: any
  cellResults: any[]
}

interface ExecutionResultWeb {
  sbxId: string
  template: string
  url: string
}

export async function POST(req: NextRequest) {
  let requestData: SandboxRequest | undefined

  try {
    requestData = await req.json()

    console.log('Sandbox request:', requestData)

    // Validate required fields
    if (!requestData || !requestData.template) {
      return NextResponse.json(
        {
          error: 'Missing required template',
          details: { template: requestData?.template }
        },
        { status: 400 }
      )
    }

    console.log(`Creating sandbox with template: ${requestData.template}`)

    // Create sandbox with retry mechanism
    const sbx = await withRetry(async () => {
      return await Sandbox.create(requestData.template, {
      metadata: {
        template: requestData.template,
        userID: requestData.userID ?? '',
        teamID: requestData.teamID ?? '',
      },
      timeoutMs: sandboxTimeout,
      ...(requestData.teamID && requestData.accessToken
        ? {
            headers: {
              'X-Supabase-Team': requestData.teamID,
              'X-Supabase-Token': requestData.accessToken,
            },
          }
        : {}),
      })
    })

    console.log(`Sandbox created successfully: ${sbx.sandboxId}`)

    // Install packages if needed
    if (requestData.has_additional_dependencies && requestData.install_dependencies_command) {
      await sbx.commands.run(requestData.install_dependencies_command)
      console.log(
        `Installed dependencies: ${requestData.additional_dependencies?.join(', ')} in sandbox ${sbx.sandboxId}`,
      )
    }

    // Copy code to filesystem
    if (requestData.is_multi_file && requestData.files && requestData.files.length > 0) {
      // Multi-file application
      console.log(`Creating multi-file application with ${requestData.files.length} files`)

      for (const file of requestData.files) {
        let filePath = file.file_path

        if (requestData.template === 'nextjs-developer') {
          // Next.js with src directory structure
          if (file.file_path.startsWith('pages/') ||
              file.file_path.startsWith('components/') ||
              file.file_path.startsWith('lib/') ||
              file.file_path.startsWith('hooks/') ||
              file.file_path.startsWith('types/')) {
            filePath = `/home/<USER>/nextjs-app/src/${file.file_path}`
          } else if (file.file_path === 'middleware.ts' ||
                     file.file_path.startsWith('prisma/')) {
            filePath = `/home/<USER>/nextjs-app/${file.file_path}`
          } else {
            filePath = `/home/<USER>/nextjs-app/src/${file.file_path}`
          }
        } else {
          filePath = `/home/<USER>/${file.file_path}`
        }

        await sbx.files.write(filePath, file.file_content)
        console.log(`Copied ${file.file_type} file: ${filePath} in ${sbx.sandboxId}`)
      }

      // For Next.js apps, restart the development server
      if (requestData.template === 'nextjs-developer') {
        console.log('Restarting Next.js development server...')
        try {
          await sbx.commands.run('pkill -f "next dev" || true')
          await sbx.commands.run('cd /home/<USER>/nextjs-app && npm run dev', { background: true })
          console.log('Next.js development server restarted')
        } catch (error) {
          console.log('Note: Could not restart dev server, using existing process')
        }
      }
    } else {
      // Single-file application (legacy support)
      if (requestData.file_path && requestData.code) {
        let filePath = requestData.file_path

        if (requestData.template === 'nextjs-developer') {
          if (requestData.file_path.startsWith('pages/') ||
              requestData.file_path.startsWith('components/') ||
              requestData.file_path.startsWith('lib/')) {
            filePath = `/home/<USER>/nextjs-app/src/${requestData.file_path}`
          } else {
            filePath = `/home/<USER>/nextjs-app/${requestData.file_path}`
          }
        } else {
          filePath = `/home/<USER>/${requestData.file_path}`
        }

        await sbx.files.write(filePath, requestData.code)
        console.log(`Copied single file: ${filePath} in ${sbx.sandboxId}`)
      } else {
        throw new Error('No files provided for application generation')
      }
    }

    // Execute code or return URL
    if (requestData.template === 'code-interpreter-v1') {
      const { logs, error, results } = await sbx.runCode(requestData.code || '')

      return NextResponse.json({
        sbxId: sbx?.sandboxId,
        template: requestData.template,
        stdout: logs.stdout,
        stderr: logs.stderr,
        runtimeError: error,
        cellResults: results,
      } as ExecutionResultInterpreter)
    }

    return NextResponse.json({
      sbxId: sbx?.sandboxId,
      template: requestData.template,
      url: `https://${sbx?.getHost(requestData.port || 80)}`,
    } as ExecutionResultWeb)

  } catch (error) {
    console.error('Error in sandbox API:', error)

    // Handle specific E2B errors
    if (error instanceof Error) {
      if (error.message.includes('template') && error.message.includes('not found')) {
        return NextResponse.json(
          {
            error: `Template '${requestData?.template}' not found`,
            details: 'The specified sandbox template does not exist. Please check the template name.',
            availableTemplates: ['nextjs-developer', 'code-interpreter-v1']
          },
          { status: 404 }
        )
      }

      if (error.message.includes('API key') || error.message.includes('authentication')) {
        return NextResponse.json(
          {
            error: 'Authentication failed',
            details: 'E2B API key is missing or invalid'
          },
          { status: 401 }
        )
      }
    }

    // Generic error response
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
