/**
 * AG3NT Platform - Coding API Routes
 * 
 * API endpoints for autonomous coding workflow
 */

import { NextRequest, NextResponse } from 'next/server'

// Import with error handling
let codingOrchestrator: any = null
try {
  const orchestratorModule = require('@/lib/coding-workflow-orchestrator')
  codingOrchestrator = orchestratorModule.codingOrchestrator
} catch (error) {
  console.error('Failed to import coding orchestrator:', error)
}

// Fallback data for when orchestrator is not available
const fallbackData = {
  status: { isRunning: false },
  progress: {
    totalTasks: 10,
    completedTasks: 10,
    inProgressTasks: 0,
    failedTasks: 0,
    currentPhase: 'Completed',
    estimatedCompletion: Date.now(),
    activeAgents: []
  },
  tasks: [
    { id: 'db-schema', title: 'Create Database Schema', status: 'completed', type: 'database' },
    { id: 'backend-setup', title: 'Setup Backend Project', status: 'completed', type: 'backend' },
    { id: 'backend-api', title: 'Generate API Endpoints', status: 'completed', type: 'backend' },
    { id: 'frontend-setup', title: 'Setup Frontend Project', status: 'completed', type: 'frontend' },
    { id: 'frontend-components', title: 'Generate UI Components', status: 'completed', type: 'frontend' },
    { id: 'frontend-styling', title: 'Apply Design System', status: 'completed', type: 'frontend' },
    { id: 'api-integration', title: 'Integrate Frontend with API', status: 'completed', type: 'integration' },
    { id: 'unit-tests', title: 'Generate Unit Tests', status: 'completed', type: 'testing' },
    { id: 'integration-tests', title: 'Generate Integration Tests', status: 'completed', type: 'testing' },
    { id: 'deployment-config', title: 'Setup Deployment Configuration', status: 'completed', type: 'deployment' }
  ]
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'status':
        if (codingOrchestrator) {
          try {
            const isRunning = codingOrchestrator.isWorkflowRunning()
            return NextResponse.json({
              success: true,
              data: { isRunning }
            })
          } catch (error) {
            console.warn('Orchestrator status error:', error)
          }
        }
        // Fallback
        return NextResponse.json({
          success: true,
          data: fallbackData.status
        })

      case 'progress':
        if (codingOrchestrator) {
          try {
            const progress = codingOrchestrator.getProgress()
            return NextResponse.json({
              success: true,
              data: progress
            })
          } catch (error) {
            console.warn('Orchestrator progress error:', error)
          }
        }
        // Fallback
        return NextResponse.json({
          success: true,
          data: fallbackData.progress
        })

      case 'tasks':
        if (codingOrchestrator) {
          try {
            const tasks = codingOrchestrator.getTasks()
            return NextResponse.json({
              success: true,
              data: tasks
            })
          } catch (error) {
            console.warn('Orchestrator tasks error:', error)
          }
        }
        // Fallback
        return NextResponse.json({
          success: true,
          data: fallbackData.tasks
        })

      case 'streaming_updates':
        if (codingOrchestrator) {
          try {
            const updates = codingOrchestrator.getStreamingUpdates()
            return NextResponse.json({
              success: true,
              data: { updates }
            })
          } catch (error) {
            console.warn('Orchestrator streaming updates error:', error)
          }
        }
        // Fallback
        return NextResponse.json({
          success: true,
          data: { updates: [] }
        })

      case 'sandboxes':
        if (codingOrchestrator) {
          try {
            const sandboxes = codingOrchestrator.getSandboxes()
            return NextResponse.json({
              success: true,
              data: { sandboxes }
            })
          } catch (error) {
            console.warn('Orchestrator sandboxes error:', error)
          }
        }
        // Fallback
        return NextResponse.json({
          success: true,
          data: { sandboxes: [] }
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: status, progress, tasks, streaming_updates, or sandboxes'
        }, { status: 400 })
    }
  } catch (error) {
    console.error('Coding API error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if orchestrator is available
    if (!codingOrchestrator) {
      return NextResponse.json({
        success: false,
        error: 'Coding orchestrator not available'
      }, { status: 503 })
    }

    const body = await request.json()
    const { action, ...data } = body

    switch (action) {
      case 'start_coding':
        try {
          // Check if workflow is already running
          let isRunning = false
          try {
            isRunning = codingOrchestrator.isWorkflowRunning()
          } catch (error) {
            console.warn('Could not check workflow status:', error)
          }

          if (isRunning) {
            return NextResponse.json({
              success: false,
              error: 'Coding workflow is already running'
            })
          }

          // Validate plan data
          if (!data.plan) {
            return NextResponse.json({
              success: false,
              error: 'Project plan is required'
            })
          }

          console.log('🚀 Starting coding workflow from API...')

          // Start coding workflow asynchronously
          try {
            codingOrchestrator.startCodingWorkflow(data.plan).catch((error: any) => {
              console.error('Coding workflow error:', error)
            })
          } catch (error) {
            console.error('Failed to start coding workflow:', error)
          }

          return NextResponse.json({
            success: true,
            message: 'Coding workflow started',
            data: {
              workflowId: `coding-${Date.now()}`,
              estimatedDuration: '30-45 minutes'
            }
          })

        } catch (error) {
          console.error('Start coding error:', error)
          return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Failed to start coding workflow'
          })
        }

      case 'stop_coding':
        try {
          // TODO: Implement stop functionality
          return NextResponse.json({ 
            success: true, 
            message: 'Coding workflow stop requested' 
          })
        } catch (error) {
          console.error('Stop coding error:', error)
          return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Failed to stop coding workflow'
          })
        }

      default:
        return NextResponse.json({ 
          success: false, 
          error: 'Invalid action. Use: start_coding or stop_coding' 
        }, { status: 400 })
    }
  } catch (error) {
    console.error('Coding API error:', error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}
